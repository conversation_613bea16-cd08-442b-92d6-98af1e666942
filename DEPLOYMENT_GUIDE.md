# AstroConnect Complete Deployment Guide

This comprehensive guide covers deploying AstroConnect with local PostgreSQL database on Ubuntu VPS.

## 🎯 Overview

This deployment guide covers:
- Setting up local PostgreSQL database (replacing remote Prisma database)
- Deploying the Next.js application
- Configuring production environment
- Setting up process management
- Implementing security measures

## 📋 Prerequisites

- Ubuntu VPS server (18.04+ recommended)
- Domain name pointed to your server
- SSH access with sudo privileges
- Node.js 18+ installed
- Git installed

## 🚀 Step 1: Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y curl wget git nginx certbot python3-certbot-nginx

# Install Node.js (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Create application user
sudo adduser astroconnect
sudo usermod -aG sudo astroconnect
```

## 🗄️ Step 2: Database Setup

Follow the detailed instructions in `PRODUCTION_DATABASE_SETUP.md` or use the quick setup:

```bash
# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y
sudo systemctl start postgresql && sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql << EOF
CREATE DATABASE astroconnect_production;
CREATE USER astroconnect_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE astroconnect_production TO astroconnect_user;
ALTER USER astroconnect_user CREATEDB;
\c astroconnect_production;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
\q
EOF
```

## 📁 Step 3: Application Deployment

```bash
# Switch to application user
sudo su - astroconnect

# Clone repository
git clone https://github.com/your-username/astroconnect.git
cd astroconnect

# Install dependencies
npm install

# Create production environment file
cp .env.production .env.production.local
nano .env.production.local
```

Update the environment variables:
```env
# Database Configuration
DATABASE_URL="postgresql://astroconnect_user:your_secure_password@localhost:5432/astroconnect_production"

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production

# API Keys
GEMINI_API_KEY=your_gemini_api_key

# JWT Secret (generate a secure one)
JWT_SECRET=your_very_secure_jwt_secret_key_here

# Security
NEXT_TELEMETRY_DISABLED=1
```

```bash
# Run database migrations
npx prisma migrate deploy

# Build the application
npm run build

# Test the application
npm start
# Press Ctrl+C to stop after testing
```

## 🔧 Step 4: Process Management with PM2

```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'astroconnect',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/astroconnect',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/home/<USER>/logs/err.log',
    out_file: '/home/<USER>/logs/out.log',
    log_file: '/home/<USER>/logs/combined.log',
    time: true
  }]
};
EOF

# Create logs directory
mkdir -p /home/<USER>/logs

# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
# Follow the instructions provided by the command above
```

## 🌐 Step 5: Nginx Configuration

```bash
# Exit from astroconnect user
exit

# Create Nginx configuration
sudo nano /etc/nginx/sites-available/astroconnect
```

Add the following configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;
}
```

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/astroconnect /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## 🔒 Step 6: SSL Certificate with Let's Encrypt

```bash
# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run

# Set up automatic renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔥 Step 7: Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Check firewall status
sudo ufw status
```

## 📊 Step 8: Monitoring and Logging

```bash
# Install monitoring tools (optional)
sudo apt install htop iotop

# Set up log rotation for PM2
sudo nano /etc/logrotate.d/pm2-astroconnect
```

Add log rotation configuration:
```
/home/<USER>/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    notifempty
    create 644 astroconnect astroconnect
    postrotate
        sudo -u astroconnect pm2 reloadLogs
    endscript
}
```

## 🔄 Step 9: Deployment Script

Create an automated deployment script:

```bash
sudo nano /home/<USER>/deploy.sh
```

```bash
#!/bin/bash
set -e

echo "🚀 Starting AstroConnect deployment..."

# Navigate to application directory
cd /home/<USER>/astroconnect

# Pull latest changes
git pull origin main

# Install dependencies
npm install

# Run database migrations
npx prisma migrate deploy

# Build application
npm run build

# Restart PM2 process
pm2 restart astroconnect

echo "✅ Deployment completed successfully!"
echo "🌐 Application is running at: https://your-domain.com"
```

```bash
# Make script executable
chmod +x /home/<USER>/deploy.sh
```

## 🧪 Step 10: Testing and Verification

```bash
# Check application status
sudo -u astroconnect pm2 status

# Check application logs
sudo -u astroconnect pm2 logs astroconnect

# Test database connection
sudo -u astroconnect psql -U astroconnect_user -d astroconnect_production -h localhost -c "SELECT COUNT(*) FROM users;"

# Test application endpoints
curl -I https://your-domain.com
curl -I https://your-domain.com/api/health

# Check SSL certificate
curl -I https://your-domain.com | grep -i ssl
```

## 🚨 Troubleshooting

### Common Issues:

1. **Application won't start**:
   ```bash
   sudo -u astroconnect pm2 logs astroconnect
   sudo -u astroconnect pm2 restart astroconnect
   ```

2. **Database connection issues**:
   ```bash
   sudo systemctl status postgresql
   sudo -u postgres psql -c "\l"
   ```

3. **Nginx issues**:
   ```bash
   sudo nginx -t
   sudo systemctl status nginx
   sudo tail -f /var/log/nginx/error.log
   ```

4. **SSL certificate issues**:
   ```bash
   sudo certbot certificates
   sudo certbot renew --dry-run
   ```

## 📋 Maintenance Tasks

### Daily:
- Monitor application logs
- Check system resources

### Weekly:
- Review security logs
- Update system packages
- Check backup integrity

### Monthly:
- Review and rotate logs
- Update dependencies
- Security audit

## 🔗 Useful Commands

```bash
# Application management
sudo -u astroconnect pm2 status
sudo -u astroconnect pm2 restart astroconnect
sudo -u astroconnect pm2 logs astroconnect

# Database management
sudo -u postgres psql -d astroconnect_production
sudo -u astroconnect psql -U astroconnect_user -d astroconnect_production -h localhost

# System monitoring
htop
sudo systemctl status nginx
sudo systemctl status postgresql

# Log monitoring
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
sudo -u astroconnect pm2 logs astroconnect --lines 100
```

## ✅ Deployment Checklist

- [ ] Server prepared and updated
- [ ] PostgreSQL installed and configured
- [ ] Database and user created
- [ ] Application cloned and dependencies installed
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Application builds successfully
- [ ] PM2 configured and application running
- [ ] Nginx configured and running
- [ ] SSL certificate obtained and configured
- [ ] Firewall configured
- [ ] Monitoring and logging set up
- [ ] Deployment script created
- [ ] All tests passing
- [ ] Domain resolving correctly
- [ ] Admin panel accessible
- [ ] Birth chart functionality working

---

**🎉 Congratulations!** Your AstroConnect application is now deployed with local PostgreSQL database and should be running smoothly without the previous schema mismatch errors.
