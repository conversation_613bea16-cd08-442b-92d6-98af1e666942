# AstroConnect Remote Prisma Database Setup Guide

This guide provides step-by-step instructions to deploy AstroConnect using the remote Prisma database for both development and production.

## 🎯 Overview

We're using remote Prisma database for:
- Simplified deployment (no database server setup required)
- Managed database service with automatic backups
- Same database for development and production consistency
- Built-in connection pooling and optimization

## 📋 Prerequisites

- Ubuntu VPS server with sudo access
- Node.js 18+ and npm installed
- Git installed
- Domain name pointed to your server (optional)

## 🚀 Step 1: Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required packages (no PostgreSQL needed)
sudo apt install -y curl wget git nginx certbot python3-certbot-nginx

# Install Node.js (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2
```

## 📁 Step 2: Application Deployment

```bash
# Create application user (optional but recommended)
sudo adduser astroconnect
sudo usermod -aG sudo astroconnect

# Switch to application user
sudo su - astroconnect

# Clone your repository
git clone https://github.com/your-username/astroconnect.git
cd astroconnect

# Install dependencies
npm install
```

## ⚙️ Step 3: Environment Configuration

Create your production environment file:

```bash
# Create production environment file
cp .env.production .env.production.local
nano .env.production.local
```

Update the environment variables with your remote Prisma database:

```env
# Remote Prisma Database Configuration
DATABASE_URL="postgres://3e4e17625c41455c15cb2e870baabff88f88fd6fe75e164f95d77e8f3a9f3693:<EMAIL>:5432/?sslmode=require"

# Prisma Accelerate URL (optional - for enhanced performance)
PRISMA_DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19lcXhnOVJ0RTJzMFcxR1ZOcTQ5MWgiLCJhcGlfa2V5IjoiMDFLMDFKMTcyNTM3U0VISDVQWFdINzBBWjEiLCJ0ZW5hbnRfaWQiOiIzZTRlMTc2MjVjNDE0NTVjMTVjYjJlODcwYmFhYmZmODhmODhmZDZmZTc1ZTE2NGY5NWQ3N2U4ZjNhOWYzNjkzIiwiaW50ZXJuYWxfc2VjcmV0IjoiYjZjMDMwYTMtZmU3Yy00NmFmLTk4NjUtZTY0YzhiYThiN2E3In0.PL9Q417x-7W29oOnTkvDroPL6ZWT3noz8omtmE0A5kQ"

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production

# API Keys
GEMINI_API_KEY=your_gemini_api_key

# JWT Secret (generate a secure one)
JWT_SECRET=your_very_secure_jwt_secret_key_here

# Security
NEXT_TELEMETRY_DISABLED=1
```

## 🗄️ Step 4: Database Setup

```bash
# Generate Prisma client
npx prisma generate

# Check migration status (should show all migrations applied)
npx prisma migrate status

# If migrations are not applied, run:
npx prisma migrate deploy

# Build the application
npm run build

# Test the application
npm start
# Press Ctrl+C to stop after testing
```

## 🔧 Step 5: Process Management with PM2

```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'astroconnect',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/astroconnect',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/home/<USER>/logs/err.log',
    out_file: '/home/<USER>/logs/out.log',
    log_file: '/home/<USER>/logs/combined.log',
    time: true
  }]
};
EOF

# Create logs directory
mkdir -p /home/<USER>/logs

# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
# Follow the instructions provided by the command above
```

## 🌐 Step 6: Nginx Configuration

```bash
# Exit from astroconnect user
exit

# Create Nginx configuration
sudo nano /etc/nginx/sites-available/astroconnect
```

Add the following configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;
}
```

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/astroconnect /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## 🔒 Step 7: SSL Certificate with Let's Encrypt

```bash
# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run

# Set up automatic renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔥 Step 8: Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Check firewall status
sudo ufw status
```

## 🧪 Step 9: Testing and Verification

```bash
# Check application status
sudo -u astroconnect pm2 status

# Check application logs
sudo -u astroconnect pm2 logs astroconnect

# Test database connection
sudo -u astroconnect npx prisma migrate status

# Test application endpoints
curl -I https://your-domain.com
curl -I https://your-domain.com/api/health

# Check SSL certificate
curl -I https://your-domain.com | grep -i ssl
```

## 🚨 Troubleshooting

### Common Issues:

1. **Application won't start**:
   ```bash
   sudo -u astroconnect pm2 logs astroconnect
   sudo -u astroconnect pm2 restart astroconnect
   ```

2. **Database connection issues**:
   ```bash
   npx prisma migrate status
   npx prisma generate
   ```

3. **Nginx issues**:
   ```bash
   sudo nginx -t
   sudo systemctl status nginx
   sudo tail -f /var/log/nginx/error.log
   ```

4. **SSL certificate issues**:
   ```bash
   sudo certbot certificates
   sudo certbot renew --dry-run
   ```

5. **Environment variable issues**:
   ```bash
   # Check if environment file is loaded
   sudo -u astroconnect cat .env.production.local
   ```

## ✅ Verification Checklist

- [ ] Server prepared and updated
- [ ] Application cloned and dependencies installed
- [ ] Environment variables configured with remote Prisma database
- [ ] Database migrations applied successfully
- [ ] Application builds and starts without errors
- [ ] PM2 configured and application running
- [ ] Nginx configured and running
- [ ] SSL certificate obtained and configured
- [ ] Firewall configured
- [ ] Admin panel accessible
- [ ] Birth chart functionality working (no schema errors)
- [ ] All tests passing

## 📞 Next Steps

1. Test all application features
2. Set up monitoring (optional)
3. Configure SSL certificates
4. Set up log rotation
5. Document your specific configuration

## 🔗 Useful Commands

```bash
# Application management
sudo -u astroconnect pm2 status
sudo -u astroconnect pm2 restart astroconnect
sudo -u astroconnect pm2 logs astroconnect

# Database management
npx prisma migrate status
npx prisma generate
npx prisma studio  # Database browser

# System monitoring
htop
sudo systemctl status nginx

# Log monitoring
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
sudo -u astroconnect pm2 logs astroconnect --lines 100
```

## 🚀 Quick Start Commands

For experienced users, here's the condensed version:

```bash
# Server setup
sudo apt update && sudo apt install -y git nginx nodejs npm
sudo npm install -g pm2

# Application deployment
git clone https://github.com/your-username/astroconnect.git
cd astroconnect
npm install

# Environment configuration
cp .env.production .env.production.local
# Edit .env.production.local with your remote Prisma database URLs

# Database and build
npx prisma generate
npx prisma migrate status  # Should show all applied
npm run build

# Process management
pm2 start ecosystem.config.js
pm2 save && pm2 startup

# Web server
sudo nginx -t && sudo systemctl restart nginx
sudo certbot --nginx -d your-domain.com
```

## 📋 Deployment Checklist

- [ ] PostgreSQL installed and running
- [ ] Database and user created with proper permissions
- [ ] Environment variables updated in production
- [ ] All migrations applied successfully
- [ ] Application builds without errors
- [ ] Application starts and connects to database
- [ ] Admin panel accessible
- [ ] Birth chart functionality working (no schema errors)
- [ ] Backup system configured
- [ ] Security measures implemented
- [ ] SSL certificates configured
- [ ] Monitoring set up (optional)

---

**⚠️ Important Notes:**
- Replace `your-domain.com` with your actual domain name
- Replace `your_gemini_api_key` with your actual Gemini API key
- Replace `your_very_secure_jwt_secret_key_here` with a strong JWT secret
- Test thoroughly before going live
- The remote Prisma database is already configured with all required schema
- No local database setup required - everything uses the remote Prisma database
- Same database for development and production ensures consistency
