# AstroConnect Remote Prisma Database Deployment Guide

## 🎯 Overview

This guide covers deploying AstroConnect using remote Prisma database for both development and production environments.

### ✅ Benefits of Remote Prisma Database
- **No database server setup required** - Prisma manages the database infrastructure
- **Automatic backups and maintenance** - Built-in backup and recovery
- **Same database for dev/prod** - Ensures consistency across environments
- **Built-in connection pooling** - Optimized performance
- **Managed security** - Database security handled by Prisma
- **Scalability** - Automatic scaling based on usage

## 📋 Prerequisites

- Remote Prisma database credentials (provided)
- Ubuntu VPS server with sudo access
- Node.js 18+ and npm installed
- Domain name (optional but recommended)

## 🔧 Database Configuration

### Current Database Setup
```env
# Primary Database URL
DATABASE_URL="postgres://3e4e17625c41455c15cb2e870baabff88f88fd6fe75e164f95d77e8f3a9f3693:<EMAIL>:5432/?sslmode=require"

# Prisma Accelerate URL (for enhanced performance)
PRISMA_DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19lcXhnOVJ0RTJzMFcxR1ZOcTQ5MWgiLCJhcGlfa2V5IjoiMDFLMDFKMTcyNTM3U0VISDVQWFdINzBBWjEiLCJ0ZW5hbnRfaWQiOiIzZTRlMTc2MjVjNDE0NTVjMTVjYjJlODcwYmFhYmZmODhmODhmZDZmZTc1ZTE2NGY5NWQ3N2U4ZjNhOWYzNjkzIiwiaW50ZXJuYWxfc2VjcmV0IjoiYjZjMDMwYTMtZmU3Yy00NmFmLTk4NjUtZTY0YzhiYThiN2E3In0.PL9Q417x-7W29oOnTkvDroPL6ZWT3noz8omtmE0A5kQ"
```

### ✅ Schema Status
- All migrations have been applied to the remote database
- Schema includes all required columns for enhanced Vedic astrology features
- No schema mismatch issues

## 🚀 Production Deployment Steps

### Step 1: Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y curl wget git nginx certbot python3-certbot-nginx

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Create application user
sudo adduser astroconnect
sudo usermod -aG sudo astroconnect
```

### Step 2: Application Deployment
```bash
# Switch to application user
sudo su - astroconnect

# Clone repository
git clone https://github.com/your-username/astroconnect.git
cd astroconnect

# Install dependencies
npm install
```

### Step 3: Environment Configuration
```bash
# Create production environment file
cp .env.production .env.production.local
nano .env.production.local
```

**Environment Variables:**
```env
# Remote Prisma Database (same for dev and prod)
DATABASE_URL="postgres://3e4e17625c41455c15cb2e870baabff88f88fd6fe75e164f95d77e8f3a9f3693:<EMAIL>:5432/?sslmode=require"

# Prisma Accelerate (optional - for performance)
PRISMA_DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19lcXhnOVJ0RTJzMFcxR1ZOcTQ5MWgiLCJhcGlfa2V5IjoiMDFLMDFKMTcyNTM3U0VISDVQWFdINzBBWjEiLCJ0ZW5hbnRfaWQiOiIzZTRlMTc2MjVjNDE0NTVjMTVjYjJlODcwYmFhYmZmODhmODhmZDZmZTc1ZTE2NGY5NWQ3N2U4ZjNhOWYzNjkzIiwiaW50ZXJuYWxfc2VjcmV0IjoiYjZjMDMwYTMtZmU3Yy00NmFmLTk4NjUtZTY0YzhiYThiN2E3In0.PL9Q417x-7W29oOnTkvDroPL6ZWT3noz8omtmE0A5kQ"

# Application Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production

# API Keys
GEMINI_API_KEY=your_gemini_api_key_here

# Security
JWT_SECRET=your_very_secure_jwt_secret_key_here
NEXT_TELEMETRY_DISABLED=1

# Database Connection Pool
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_POOL_IDLE_TIMEOUT=30000

# Application Settings
PORT=3000
HOSTNAME=0.0.0.0
```

### Step 4: Database Setup
```bash
# Generate Prisma client
npx prisma generate

# Verify migration status (should show all applied)
npx prisma migrate status

# Build application
npm run build

# Test application
npm start
# Press Ctrl+C after testing
```

### Step 5: Process Management
```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'astroconnect',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/astroconnect',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/home/<USER>/logs/err.log',
    out_file: '/home/<USER>/logs/out.log',
    log_file: '/home/<USER>/logs/combined.log',
    time: true
  }]
};
EOF

# Create logs directory
mkdir -p /home/<USER>/logs

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Step 6: Web Server Configuration
```bash
# Exit from astroconnect user
exit

# Configure Nginx
sudo nano /etc/nginx/sites-available/astroconnect
```

**Nginx Configuration:**
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

```bash
# Enable site and restart Nginx
sudo ln -s /etc/nginx/sites-available/astroconnect /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Step 7: SSL Certificate
```bash
# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test renewal
sudo certbot renew --dry-run

# Set up auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Step 8: Security & Firewall
```bash
# Configure firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

## 🧪 Testing & Verification

### Application Tests
```bash
# Check PM2 status
sudo -u astroconnect pm2 status

# Check logs
sudo -u astroconnect pm2 logs astroconnect

# Test endpoints
curl -I https://your-domain.com
curl -I https://your-domain.com/api/health

# Test admin panel
curl -I https://your-domain.com/admin
```

### Database Tests
```bash
# Check migration status
sudo -u astroconnect npx prisma migrate status

# Test database connection
sudo -u astroconnect npx prisma studio
# Access at http://localhost:5555
```

## 🔄 Deployment Script

Create automated deployment:
```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 Deploying AstroConnect..."

cd /home/<USER>/astroconnect

# Pull latest changes
git pull origin main

# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Build application
npm run build

# Restart PM2
pm2 restart astroconnect

echo "✅ Deployment completed!"
echo "🌐 Application: https://your-domain.com"
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection**:
   ```bash
   npx prisma migrate status
   npx prisma generate
   ```

2. **Application Errors**:
   ```bash
   pm2 logs astroconnect
   pm2 restart astroconnect
   ```

3. **Environment Issues**:
   ```bash
   cat .env.production.local
   ```

## ✅ Deployment Checklist

- [ ] Server prepared and updated
- [ ] Application cloned and dependencies installed
- [ ] Environment variables configured
- [ ] Prisma client generated
- [ ] Application builds successfully
- [ ] PM2 configured and running
- [ ] Nginx configured and running
- [ ] SSL certificate configured
- [ ] Firewall configured
- [ ] All endpoints responding
- [ ] Admin panel accessible
- [ ] Birth chart functionality working

## 🎉 Success!

Your AstroConnect application is now deployed with remote Prisma database:
- **Database**: Fully managed by Prisma
- **Performance**: Optimized with connection pooling
- **Security**: Managed by Prisma infrastructure
- **Backups**: Automatic backups included
- **Consistency**: Same database for dev and production

---

**🔗 Useful Links:**
- Application: https://your-domain.com
- Admin Panel: https://your-domain.com/admin
- Health Check: https://your-domain.com/api/health
