-- Add missing columns to birth_charts table in remote Prisma database
-- These columns are required for the enhanced Vedic astrology features

ALTER TABLE "birth_charts" 
ADD COLUMN IF NOT EXISTS "panchang" JSONB,
ADD COLUMN IF NOT EXISTS "dosha_analysis" JSONB,
ADD COLUMN IF NOT EXISTS "yoga_analysis" JSONB,
ADD COLUMN IF NOT EXISTS "planetary_strengths" JSONB,
ADD COLUMN IF NOT EXISTS "divisional_charts" JSONB;
