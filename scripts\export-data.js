#!/usr/bin/env node

/**
 * AstroConnect Data Export Script
 * Exports data from remote Prisma database for migration to local PostgreSQL
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function exportData() {
  console.log('🚀 Starting data export from remote database...');
  
  try {
    const exportData = {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      data: {}
    };

    // Export Users
    console.log('📊 Exporting users...');
    const users = await prisma.user.findMany({
      include: {
        qrCodeMappings: true,
        personalHoroscopes: true,
        birthChart: true
      }
    });
    exportData.data.users = users;
    console.log(`✅ Exported ${users.length} users`);

    // Export Admins
    console.log('👥 Exporting admins...');
    const admins = await prisma.admin.findMany();
    exportData.data.admins = admins;
    console.log(`✅ Exported ${admins.length} admins`);

    // Export Daily Zodiac Readings
    console.log('🌟 Exporting daily zodiac readings...');
    const dailyReadings = await prisma.dailyZodiacReading.findMany({
      orderBy: { date: 'desc' },
      take: 1000 // Export last 1000 readings
    });
    exportData.data.dailyZodiacReadings = dailyReadings;
    console.log(`✅ Exported ${dailyReadings.length} daily readings`);

    // Export Horoscopes
    console.log('🔮 Exporting horoscopes...');
    const horoscopes = await prisma.horoscope.findMany();
    exportData.data.horoscopes = horoscopes;
    console.log(`✅ Exported ${horoscopes.length} horoscopes`);

    // Export System Settings
    console.log('⚙️ Exporting system settings...');
    const systemSettings = await prisma.systemSettings.findMany();
    exportData.data.systemSettings = systemSettings;
    console.log(`✅ Exported ${systemSettings.length} system settings`);

    // Export Translation Cache
    console.log('🌐 Exporting translation cache...');
    const translationCache = await prisma.translationCache.findMany({
      take: 5000 // Export last 5000 translations
    });
    exportData.data.translationCache = translationCache;
    console.log(`✅ Exported ${translationCache.length} translations`);

    // Write to file
    const exportPath = path.join(__dirname, '..', 'exported-data.json');
    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));
    
    console.log('✅ Data export completed successfully!');
    console.log(`📁 Export file: ${exportPath}`);
    console.log(`📊 Export summary:`);
    console.log(`   - Users: ${users.length}`);
    console.log(`   - Admins: ${admins.length}`);
    console.log(`   - Daily Readings: ${dailyReadings.length}`);
    console.log(`   - Horoscopes: ${horoscopes.length}`);
    console.log(`   - System Settings: ${systemSettings.length}`);
    console.log(`   - Translation Cache: ${translationCache.length}`);
    
    // Calculate file size
    const stats = fs.statSync(exportPath);
    const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`📦 Export file size: ${fileSizeInMB} MB`);

  } catch (error) {
    console.error('❌ Error during data export:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run export if called directly
if (require.main === module) {
  exportData().catch(console.error);
}

module.exports = { exportData };
