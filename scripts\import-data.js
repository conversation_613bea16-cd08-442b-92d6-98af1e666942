#!/usr/bin/env node

/**
 * AstroConnect Data Import Script
 * Imports data from exported JSON file to local PostgreSQL database
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function importData() {
  console.log('🚀 Starting data import to local database...');
  
  try {
    // Read export file
    const exportPath = path.join(__dirname, '..', 'exported-data.json');
    if (!fs.existsSync(exportPath)) {
      throw new Error(`Export file not found: ${exportPath}`);
    }

    const exportData = JSON.parse(fs.readFileSync(exportPath, 'utf8'));
    console.log(`📁 Reading export from: ${exportData.timestamp}`);

    // Clear existing data (optional - comment out if you want to preserve existing data)
    console.log('🧹 Clearing existing data...');
    await prisma.translationCache.deleteMany();
    await prisma.dailyZodiacReading.deleteMany();
    await prisma.horoscope.deleteMany();
    await prisma.birthChart.deleteMany();
    await prisma.personalHoroscope.deleteMany();
    await prisma.qrCodeMapping.deleteMany();
    await prisma.user.deleteMany();
    await prisma.admin.deleteMany();
    await prisma.systemSettings.deleteMany();

    // Import System Settings first
    if (exportData.data.systemSettings && exportData.data.systemSettings.length > 0) {
      console.log('⚙️ Importing system settings...');
      for (const setting of exportData.data.systemSettings) {
        await prisma.systemSettings.create({
          data: {
            defaultLanguage: setting.defaultLanguage,
            createdAt: new Date(setting.createdAt),
            updatedAt: new Date(setting.updatedAt)
          }
        });
      }
      console.log(`✅ Imported ${exportData.data.systemSettings.length} system settings`);
    }

    // Import Admins
    if (exportData.data.admins && exportData.data.admins.length > 0) {
      console.log('👥 Importing admins...');
      for (const admin of exportData.data.admins) {
        await prisma.admin.create({
          data: {
            id: admin.id,
            email: admin.email,
            password: admin.password,
            name: admin.name,
            role: admin.role,
            isActive: admin.isActive,
            lastLogin: admin.lastLogin ? new Date(admin.lastLogin) : null,
            createdBy: admin.createdBy,
            createdAt: new Date(admin.createdAt),
            updatedAt: new Date(admin.updatedAt)
          }
        });
      }
      console.log(`✅ Imported ${exportData.data.admins.length} admins`);
    }

    // Import Users (without relations first)
    if (exportData.data.users && exportData.data.users.length > 0) {
      console.log('👤 Importing users...');
      for (const user of exportData.data.users) {
        await prisma.user.create({
          data: {
            id: user.id,
            email: user.email,
            name: user.name,
            phoneNumber: user.phoneNumber,
            address: user.address,
            zodiacSign: user.zodiacSign,
            birthDate: new Date(user.birthDate),
            birthTime: user.birthTime,
            birthPlace: user.birthPlace,
            birthLatitude: user.birthLatitude,
            birthLongitude: user.birthLongitude,
            qrToken: user.qrToken,
            languagePreference: user.languagePreference,
            createdAt: new Date(user.createdAt),
            updatedAt: new Date(user.updatedAt)
          }
        });
      }
      console.log(`✅ Imported ${exportData.data.users.length} users`);

      // Import QR Code Mappings
      console.log('🔗 Importing QR code mappings...');
      let qrMappingCount = 0;
      for (const user of exportData.data.users) {
        if (user.qrCodeMappings && user.qrCodeMappings.length > 0) {
          for (const mapping of user.qrCodeMappings) {
            await prisma.qrCodeMapping.create({
              data: {
                id: mapping.id,
                qrToken: mapping.qrToken,
                userId: mapping.userId,
                createdAt: new Date(mapping.createdAt),
                lastScanned: mapping.lastScanned ? new Date(mapping.lastScanned) : null,
                scanCount: mapping.scanCount
              }
            });
            qrMappingCount++;
          }
        }
      }
      console.log(`✅ Imported ${qrMappingCount} QR code mappings`);

      // Import Personal Horoscopes
      console.log('📜 Importing personal horoscopes...');
      let personalHoroscopeCount = 0;
      for (const user of exportData.data.users) {
        if (user.personalHoroscopes && user.personalHoroscopes.length > 0) {
          for (const horoscope of user.personalHoroscopes) {
            await prisma.personalHoroscope.create({
              data: {
                id: horoscope.id,
                userId: horoscope.userId,
                title: horoscope.title,
                content: horoscope.content,
                isActive: horoscope.isActive,
                language: horoscope.language,
                createdAt: new Date(horoscope.createdAt),
                updatedAt: new Date(horoscope.updatedAt)
              }
            });
            personalHoroscopeCount++;
          }
        }
      }
      console.log(`✅ Imported ${personalHoroscopeCount} personal horoscopes`);

      // Import Birth Charts
      console.log('🌟 Importing birth charts...');
      let birthChartCount = 0;
      for (const user of exportData.data.users) {
        if (user.birthChart) {
          const chart = user.birthChart;
          await prisma.birthChart.create({
            data: {
              id: chart.id,
              userId: chart.userId,
              birthDateTime: new Date(chart.birthDateTime),
              birthPlace: chart.birthPlace,
              birthLatitude: chart.birthLatitude,
              birthLongitude: chart.birthLongitude,
              timezone: chart.timezone,
              planetPositions: chart.planetPositions,
              housePositions: chart.housePositions,
              aspects: chart.aspects,
              nakshatras: chart.nakshatras,
              dashas: chart.dashas,
              lagnaChart: chart.lagnaChart,
              navamsaChart: chart.navamsaChart,
              chandraChart: chart.chandraChart,
              karakTable: chart.karakTable,
              avasthaTable: chart.avasthaTable,
              planetaryDetails: chart.planetaryDetails,
              vimshottariDasha: chart.vimshottariDasha,
              ashtakavarga: chart.ashtakavarga,
              panchang: chart.panchang,
              doshaAnalysis: chart.doshaAnalysis,
              yogaAnalysis: chart.yogaAnalysis,
              planetaryStrengths: chart.planetaryStrengths,
              divisionalCharts: chart.divisionalCharts,
              ascendant: chart.ascendant,
              moonSign: chart.moonSign,
              sunSign: chart.sunSign,
              generalReading: chart.generalReading,
              strengthsWeaknesses: chart.strengthsWeaknesses,
              careerGuidance: chart.careerGuidance,
              relationshipGuidance: chart.relationshipGuidance,
              healthGuidance: chart.healthGuidance,
              readingsEn: chart.readingsEn,
              readingsSi: chart.readingsSi,
              calculatedAt: new Date(chart.calculatedAt),
              updatedAt: new Date(chart.updatedAt)
            }
          });
          birthChartCount++;
        }
      }
      console.log(`✅ Imported ${birthChartCount} birth charts`);
    }

    // Import Daily Zodiac Readings
    if (exportData.data.dailyZodiacReadings && exportData.data.dailyZodiacReadings.length > 0) {
      console.log('📅 Importing daily zodiac readings...');
      for (const reading of exportData.data.dailyZodiacReadings) {
        await prisma.dailyZodiacReading.create({
          data: {
            id: reading.id,
            zodiacSign: reading.zodiacSign,
            date: new Date(reading.date),
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: reading.compatibility,
            language: reading.language,
            createdAt: new Date(reading.createdAt),
            updatedAt: new Date(reading.updatedAt)
          }
        });
      }
      console.log(`✅ Imported ${exportData.data.dailyZodiacReadings.length} daily readings`);
    }

    // Import Horoscopes
    if (exportData.data.horoscopes && exportData.data.horoscopes.length > 0) {
      console.log('🔮 Importing horoscopes...');
      for (const horoscope of exportData.data.horoscopes) {
        await prisma.horoscope.create({
          data: {
            id: horoscope.id,
            zodiacSign: horoscope.zodiacSign,
            type: horoscope.type,
            content: horoscope.content,
            date: new Date(horoscope.date),
            language: horoscope.language,
            createdAt: new Date(horoscope.createdAt)
          }
        });
      }
      console.log(`✅ Imported ${exportData.data.horoscopes.length} horoscopes`);
    }

    // Import Translation Cache
    if (exportData.data.translationCache && exportData.data.translationCache.length > 0) {
      console.log('🌐 Importing translation cache...');
      for (const translation of exportData.data.translationCache) {
        await prisma.translationCache.create({
          data: {
            id: translation.id,
            originalText: translation.originalText,
            translatedText: translation.translatedText,
            sourceLanguage: translation.sourceLanguage,
            targetLanguage: translation.targetLanguage,
            createdAt: new Date(translation.createdAt)
          }
        });
      }
      console.log(`✅ Imported ${exportData.data.translationCache.length} translations`);
    }

    console.log('✅ Data import completed successfully!');
    console.log('🎉 Your local database is now ready to use!');

  } catch (error) {
    console.error('❌ Error during data import:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run import if called directly
if (require.main === module) {
  importData().catch(console.error);
}

module.exports = { importData };
