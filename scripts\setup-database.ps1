# AstroConnect Database Setup Script (PowerShell)
# This script sets up PostgreSQL for local development

param(
    [switch]$Force,
    [string]$PostgresPassword = "postgres"
)

Write-Host "🚀 Setting up AstroConnect Local Database..." -ForegroundColor Blue

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if PostgreSQL is installed
try {
    $null = Get-Command psql -ErrorAction Stop
    Write-Success "PostgreSQL is installed"
} catch {
    Write-Error "PostgreSQL is not installed. Please install PostgreSQL first."
    Write-Host ""
    Write-Host "Download PostgreSQL from: https://www.postgresql.org/download/windows/"
    Write-Host "Or install via Chocolatey: choco install postgresql"
    exit 1
}

# Check if PostgreSQL service is running
$pgService = Get-Service -Name "postgresql*" -ErrorAction SilentlyContinue
if ($pgService -and $pgService.Status -eq "Running") {
    Write-Success "PostgreSQL service is running"
} else {
    Write-Warning "PostgreSQL service is not running or not found"
    Write-Host "Please ensure PostgreSQL service is started manually"
}

# Create databases
Write-Status "Creating databases..."

$env:PGPASSWORD = $PostgresPassword

try {
    # Create development database
    Write-Status "Creating development database..."
    & psql -U postgres -h localhost -c "CREATE DATABASE astroconnect_dev;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Development database created"
    } else {
        Write-Warning "Development database might already exist"
    }

    # Create production database
    Write-Status "Creating production database..."
    & psql -U postgres -h localhost -c "CREATE DATABASE astroconnect_production;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Production database created"
    } else {
        Write-Warning "Production database might already exist"
    }

    # Enable UUID extension for development database
    Write-Status "Setting up development database extensions..."
    & psql -U postgres -h localhost -d astroconnect_dev -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";" 2>$null

    # Enable UUID extension for production database
    Write-Status "Setting up production database extensions..."
    & psql -U postgres -h localhost -d astroconnect_production -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";" 2>$null

    Write-Success "Database setup completed!"

} catch {
    Write-Error "Failed to create databases: $_"
    exit 1
} finally {
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Run: npm run db:migrate" -ForegroundColor White
Write-Host "2. Run: npm run dev" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Database Connection Strings:" -ForegroundColor Yellow
Write-Host "Development: postgresql://postgres:$PostgresPassword@localhost:5432/astroconnect_dev" -ForegroundColor White
Write-Host "Production:  postgresql://postgres:$PostgresPassword@localhost:5432/astroconnect_production" -ForegroundColor White
