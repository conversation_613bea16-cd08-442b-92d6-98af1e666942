#!/bin/bash

# AstroConnect Database Setup Script
# This script sets up PostgreSQL for local development and production

set -e

echo "🚀 Setting up AstroConnect Local Database..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    print_error "PostgreSQL is not installed. Please install PostgreSQL first."
    echo ""
    echo "Installation instructions:"
    echo "- Ubuntu/Debian: sudo apt-get install postgresql postgresql-contrib"
    echo "- CentOS/RHEL: sudo yum install postgresql-server postgresql-contrib"
    echo "- macOS: brew install postgresql"
    echo "- Windows: Download from https://www.postgresql.org/download/windows/"
    exit 1
fi

print_success "PostgreSQL is installed"

# Check if PostgreSQL service is running
if ! sudo systemctl is-active --quiet postgresql 2>/dev/null && ! brew services list | grep postgresql | grep started &>/dev/null; then
    print_warning "PostgreSQL service is not running. Starting it..."
    
    # Try different methods to start PostgreSQL
    if command -v systemctl &> /dev/null; then
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    elif command -v brew &> /dev/null; then
        brew services start postgresql
    else
        print_error "Could not start PostgreSQL service. Please start it manually."
        exit 1
    fi
fi

print_success "PostgreSQL service is running"

# Create databases and user
print_status "Creating databases and user..."

# Run the SQL setup script
if sudo -u postgres psql -f scripts/setup-local-database.sql; then
    print_success "Databases created successfully"
else
    print_warning "Database creation failed or databases already exist"
fi

# Update package.json scripts if they don't exist
print_status "Checking package.json scripts..."

if ! grep -q "db:migrate" package.json; then
    print_status "Adding database scripts to package.json..."
    # This would need to be done manually or with a more complex script
    print_warning "Please add these scripts to your package.json:"
    echo '  "db:migrate": "npx prisma migrate dev",'
    echo '  "db:reset": "npx prisma migrate reset",'
    echo '  "db:studio": "npx prisma studio",'
    echo '  "db:generate": "npx prisma generate"'
fi

print_success "Database setup completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Update your .env files with the correct database credentials"
echo "2. Run: npm run db:migrate"
echo "3. Run: npm run dev"
echo ""
echo "🔧 Database Connection Strings:"
echo "Development: postgresql://postgres:postgres@localhost:5432/astroconnect_dev"
echo "Production:  postgresql://postgres:your_password@localhost:5432/astroconnect_production"
echo ""
echo "🎯 Optional: Use dedicated user instead of postgres:"
echo "postgresql://astroconnect_user:astroconnect_secure_password_2025@localhost:5432/astroconnect_dev"
