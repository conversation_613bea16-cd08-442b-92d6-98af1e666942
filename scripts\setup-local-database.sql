-- AstroConnect Local Database Setup Script
-- This script sets up the local PostgreSQL database for development and production

-- Create development database
CREATE DATABASE astroconnect_dev;

-- Create production database (for production server)
CREATE DATABASE astroconnect_production;

-- Create a dedicated user for the application (optional but recommended)
CREATE USER astroconnect_user WITH PASSWORD 'astroconnect_secure_password_2025';

-- Grant privileges to the user
GRANT ALL PRIVILEGES ON DATABASE astroconnect_dev TO astroconnect_user;
GRANT ALL PRIVILEGES ON DATABASE astroconnect_production TO astroconnect_user;

-- Connect to development database and set up extensions if needed
\c astroconnect_dev;

-- Enable UUID extension (required for Prisma CUID)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Connect to production database and set up extensions
\c astroconnect_production;

-- Enable UUID extension (required for Prisma CUID)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Display success message
\echo 'AstroConnect databases created successfully!'
\echo 'Development database: astroconnect_dev'
\echo 'Production database: astroconnect_production'
\echo 'User: astroconnect_user'
\echo ''
\echo 'Next steps:'
\echo '1. Run: npm run db:migrate'
\echo '2. Run: npm run db:seed (if you have seed data)'
\echo '3. Start the application: npm run dev'
